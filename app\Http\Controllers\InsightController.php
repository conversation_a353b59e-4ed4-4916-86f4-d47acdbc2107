<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Insight;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class InsightController extends Controller
{
    /**
     * Store a newly created insight in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'content' => 'required|string|max:1000',
            // In a real application, category_id should be validated
            // against the categories table
            'category_id' => 'required|integer',
            'image' => 'nullable|image|mimes:jpeg,png,gif,webp|max:2048', // 2MB max
        ]);

        $imageUrl = null;

        // Handle image upload if provided
        if ($request->hasFile('image')) {
            $image = $request->file('image');

            // Generate unique filename
            $filename = Str::uuid() . '.' . $image->getClientOriginalExtension();

            // Store in public disk under insights folder
            $path = $image->storeAs('insights', $filename, 'public');

            // Generate the public URL
            $imageUrl = Storage::url($path);
        }

        Insight::create([
            'user_id' => Auth::id(),
            'title' => substr($request->content, 0, 50), // Simple title generation
            'content' => $request->content,
            'image_url' => $imageUrl,
            'category_id' => $request->category_id,
            'status' => 'published' // Or 'pending' for admin approval
        ]);

        return back()->with('success', 'Insight published successfully!');
    }
}
